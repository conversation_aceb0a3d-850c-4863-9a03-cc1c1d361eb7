import React from "react";
import ArticleForm from "../../../../../../utilities/minitiatures/ArticleForm/ArticleForm";
import { Article, ImageFile } from "../../../../../../utilities/constants/types";
import Button from "../../../../../../utilities/minitiatures/Button/Button";
import { Modal } from "react-bootstrap";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../../../../utilities/redux/store";
import { addArticle } from "../../../../../../utilities/redux/backoffice/backofficeSlice";
import Title from "../../../../../../utilities/minitiatures/Title/Title";

/**
 * Composant pour l'ajout d'un nouvel article
 * Affiche un bouton qui ouvre un modal avec un formulaire
 * Utilise Redux pour envoyer les données à l'API
 */
const AddArticle = React.memo(() => {
    const dispatch = useDispatch<AppDispatch>();
    const [state, setState] = React.useState({
        show: false,
        loading: false,
    });

    const setShow = React.useCallback((show: boolean) => setState(s => ({ ...s, show })), []);
    const setLoading = React.useCallback((loading: boolean) => setState(s => ({ ...s, loading })), []);

    const handleArticleSubmit = React.useCallback(async (articleData: Omit<Article, 'id'> & { imageFiles: ImageFile[] }) => {
        try {
            setLoading(true);

            // Préparer les données pour l'API
            const { imageFiles, ...articleInfo } = articleData;

            // Extraire les fichiers d'images pour l'API
            const imageFilesForAPI = imageFiles
                .filter(img => img.imageData) // Seulement les images avec des fichiers
                .map(img => img.imageData as File);

            const articleForAPI = {
                ...articleInfo,
                images: imageFilesForAPI // L'API s'attend à recevoir des File[]
            };

            // Appel à l'action Redux pour ajouter l'article
            await dispatch(addArticle(articleForAPI)).unwrap();

            // Fermer le modal
            setShow(false);

            // Afficher un message de succès
            alert("Article créé avec succès !");
        } catch (error) {
            console.error("Erreur lors de la création de l'article:", error);
            alert("Erreur lors de la création de l'article. Veuillez réessayer.");
        } finally {
            setLoading(false);
        }
    }, [dispatch, setShow, setLoading]);

    return (
        <div className="add-article-container">
            <Button
                className="btn btn-secondary add-button"
                type="button"
                onClick={() => setShow(true)}
            >
                <i className="fa fa-plus"></i> Créer
            </Button>

            <Modal show={state.show} onHide={() => setShow(false)} size="xl" centered>
                <Modal.Header closeButton>
                    <Modal.Title>
                        <Title text="Création d'un article" />
                        <small className="text-muted d-block">
                            Remplir les données pour enregistrer un nouvel article
                        </small>
                    </Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <ArticleForm
                        onSubmit={handleArticleSubmit}
                        isLoading={state.loading}
                    />
                </Modal.Body>
            </Modal>
        </div>
    );
});

export default AddArticle;
