import React from "react";
import generateArray from "../../../../../../utilities/helpers/generateArray";
import ImageInputDD from "../../../../../../utilities/minitiatures/ImageInputDD/ImageInputDD";
import { ImageFile } from "../../../../../../utilities/constants/types";

type Props = {
    count?: number,
    addImage: (image: ImageFile) => void,
    removeImage: (url: string) => void,
    images: ImageFile[],
}

const AddArticleImages = React.memo((props: Props) => {
    const { count = 6, images, ...imageInputDDProps } = props;
    const length = React.useMemo(() => (images.length + 1) < count ? images.length + 1 : count, [count, images]);

    return (
        <div className="add-article-images-container d-flex gap-1 flex-wrap">
            {generateArray(length).map((any, key) => {
                any; // Éviter le warning
                return (
                    <ImageInputDD
                        key={key}
                        imageUrl={images[key]?.imageUrl}
                        {...imageInputDDProps}
                        id={'article-image-input-dd' + key}
                        size="md"
                    />
                );
            })}
        </div>
    );
});

export default AddArticleImages;
