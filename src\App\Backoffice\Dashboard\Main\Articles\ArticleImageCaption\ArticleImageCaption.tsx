import React from "react";
import { ImageFile } from "../../../../../../utilities/constants/types";

type Props = {
    image: ImageFile;
    index: number;
    onCaptionChange: (index: number, caption: string) => void;
    onRemove: (index: number) => void;
}

const ArticleImageCaption = React.memo((props: Props) => {
    const { image, index, onCaptionChange, onRemove } = props;
    const [caption, setCaption] = React.useState(image.caption || '');

    const handleCaptionChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        const newCaption = e.target.value;
        setCaption(newCaption);
        onCaptionChange(index, newCaption);
    }, [index, onCaptionChange]);

    if (!image.imageUrl) {
        return null;
    }

    return (
        <div className="article-image-caption mb-3 p-3 border rounded">
            <div className="d-flex gap-3 align-items-start">
                <div className="image-preview">
                    <img 
                        src={image.imageUrl} 
                        alt="Aperçu" 
                        style={{ 
                            width: '80px', 
                            height: '80px', 
                            objectFit: 'cover',
                            borderRadius: '4px'
                        }} 
                    />
                </div>
                <div className="flex-grow-1">
                    <label className="form-label small">Légende de l'image</label>
                    <input
                        type="text"
                        className="form-control form-control-sm"
                        value={caption}
                        onChange={handleCaptionChange}
                        placeholder="Entrez une légende pour cette image..."
                    />
                </div>
                <button
                    type="button"
                    className="btn btn-outline-danger btn-sm"
                    onClick={() => onRemove(index)}
                    title="Supprimer cette image"
                >
                    <i className="fa fa-trash"></i>
                </button>
            </div>
        </div>
    );
});

export default ArticleImageCaption;
