import React, { useState } from 'react';
import { Article, Section, Category } from '../../constants/types';
import SectionForm from '../SectionForm/SectionForm';
import AddImages from '../../../App/Backoffice/Dashboard/Main/Products/AddImages/AddImages';
import SelectedCategory from '../../../App/Backoffice/Dashboard/Main/Categories/AddCategory/SelectedCategory/SelectedCategory';
import useCategorySelect from '../CategorySelect/hooks/useCategorySelect';
import Button from '../Button/Button';
import { Image } from '../ImageInputDD/ImageInputDD';

interface ArticleFormProps {
  onSubmit: (article: Omit<Article, 'id' | 'images'> & { images: File[] }) => void;
  initialData?: Article;
  isLoading?: boolean;
}

const ArticleForm: React.FC<ArticleFormProps> = ({ onSubmit, initialData, isLoading = false }) => {
  const [title, setTitle] = useState(initialData?.title || '');
  const [author, setAuthor] = useState(initialData?.author || '');
  const [category, setCategory] = useState<Category | null>(null);
  const [sections, setSections] = useState<Section[]>(initialData?.sections || []);
  const [images, setImages] = useState<Image[]>([]);

  const categorySelect = useCategorySelect();

  const handleAddSection = () => {
    setSections([...sections, { id: Date.now(), title: '', subsections: [] }]);
  };

  const handleRemoveSection = (id: number) => {
    setSections(sections.filter(section => section.id !== id));
  };

  const handleAddImage = React.useCallback((image: Image) => {
    setImages(prev => [...prev, image]);
  }, []);

  const handleRemoveImage = React.useCallback((url: string) => {
    setImages(prev => prev.filter(image => image.imageUrl !== url));
  }, []);

  const handleCategorySelectClose = React.useCallback((selected: Category | null) => {
    setCategory(selected);
  }, []);

  const handleOpenCategorySelect = React.useCallback(() => {
    categorySelect.open(handleCategorySelectClose, category?.id);
  }, [categorySelect, handleCategorySelectClose, category]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validation basique
    if (!title.trim()) {
      alert('Le titre est requis');
      return;
    }
    if (!author.trim()) {
      alert('L\'auteur est requis');
      return;
    }

    // Extraire les fichiers d'images
    const imageFiles = images
      .filter(img => img.imageData)
      .map(img => img.imageData as File);

    onSubmit({
      title: title.trim(),
      author: author.trim(),
      category_id: category?.id || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sections,
      images: imageFiles
    });
  };

  return (
    <form onSubmit={handleSubmit} className="container-fluid">
      {/* Informations de base */}
      <div className='row mb-4'>
        <div className="col-md-6 mb-3">
          <label className="form-label fw-bold">Titre de l'article *</label>
          <input
            type="text"
            className="form-control"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            required
            placeholder="Entrez le titre de l'article..."
          />
        </div>
        <div className="col-md-6 mb-3">
          <label className="form-label fw-bold">Auteur *</label>
          <input
            type="text"
            className="form-control"
            value={author}
            onChange={(e) => setAuthor(e.target.value)}
            required
            placeholder="Nom de l'auteur..."
          />
        </div>
      </div>

      {/* Catégorie */}
      <div className="row mb-4">
        <div className="col-md-8">
          <h6 className="fw-bold">Catégorie de l'article</h6>
          <SelectedCategory category={category} />
        </div>
        <div className="col-md-4 d-flex align-items-end">
          <Button
            type="button"
            className="btn btn-outline-primary btn-sm"
            onClick={handleOpenCategorySelect}
          >
            Choisir <i className="fa fa-external-link"></i>
          </Button>
        </div>
      </div>

      {/* Sections */}
      <div className="mb-4">
        <div className="d-flex justify-content-between align-items-center mb-3">
          <label className="form-label fw-bold mb-0">Sections du contenu</label>
          <button
            type="button"
            className="btn btn-outline-primary btn-sm"
            onClick={handleAddSection}
          >
            <i className='fa fa-plus'></i> Ajouter une section
          </button>
        </div>
        {sections.length === 0 && (
          <div className="alert alert-info">
            <i className="fa fa-info-circle me-2"></i>
            Aucune section ajoutée. Cliquez sur "Ajouter une section" pour commencer.
          </div>
        )}
        {sections.map((section, index) => (
          <SectionForm
            key={section.id}
            section={section}
            onChange={(updatedSection) => {
              const newSections = [...sections];
              newSections[index] = updatedSection;
              setSections(newSections);
            }}
            onRemove={() => handleRemoveSection(section.id)}
          />
        ))}
      </div>

      {/* Images */}
      <div className="mb-4">
        <h6 className="fw-bold mb-3">Images de l'article</h6>
        <AddImages
          addImage={handleAddImage}
          removeImage={handleRemoveImage}
          images={images}
          count={6}
        />
      </div>

      {/* Bouton de soumission */}
      <div className="d-flex justify-content-end gap-2 pt-3 border-top">
        <button
          type="submit"
          className="btn btn-success"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Enregistrement...
            </>
          ) : (
            <>
              <i className="fa fa-check me-2"></i>
              Enregistrer l'article
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default ArticleForm;
