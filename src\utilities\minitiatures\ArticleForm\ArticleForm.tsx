import React, { useState } from 'react';
import { Article, Section, ImageFile } from '../../constants/types';
import SectionForm from '../SectionForm/SectionForm';
import AddArticleImages from '../../../App/Backoffice/Dashboard/Main/Articles/AddArticleImages/AddArticleImages';
import ArticleImageCaption from '../../../App/Backoffice/Dashboard/Main/Articles/ArticleImageCaption/ArticleImageCaption';

interface ArticleFormProps {
  onSubmit: (article: Omit<Article, 'id'> & { imageFiles: ImageFile[] }) => void;
  initialData?: Article;
  isLoading?: boolean;
}

const ArticleForm: React.FC<ArticleFormProps> = ({ onSubmit, initialData, isLoading = false }) => {
  const [title, setTitle] = useState(initialData?.title || '');
  const [author, setAuthor] = useState(initialData?.author || '');
  const [sections, setSections] = useState<Section[]>(initialData?.sections || []);
  const [imageFiles, setImageFiles] = useState<ImageFile[]>([]);

  const handleAddSection = () => {
    setSections([...sections, { id: Date.now(), title: '', subsections: [] }]);
  };

  const handleRemoveSection = (id: number) => {
    setSections(sections.filter(section => section.id !== id));
  };

  const handleAddImage = React.useCallback((image: ImageFile) => {
    setImageFiles(prev => [...prev, { ...image, caption: image.caption || '' }]);
  }, []);

  const handleRemoveImage = React.useCallback((url: string) => {
    setImageFiles(prev => prev.filter(image => image.imageUrl !== url));
  }, []);

  const handleImageCaptionChange = React.useCallback((index: number, caption: string) => {
    setImageFiles(prev => {
      const newImages = [...prev];
      if (newImages[index]) {
        newImages[index] = { ...newImages[index], caption };
      }
      return newImages;
    });
  }, []);

  const handleRemoveImageByIndex = React.useCallback((index: number) => {
    setImageFiles(prev => prev.filter((_, i) => i !== index));
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validation basique
    if (!title.trim()) {
      alert('Le titre est requis');
      return;
    }
    if (!author.trim()) {
      alert('L\'auteur est requis');
      return;
    }

    onSubmit({
      title: title.trim(),
      author: author.trim(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sections,
      images: [], // Les images seront gérées séparément
      imageFiles
    });
  };

  return (
    <form onSubmit={handleSubmit} className="container-fluid">
      {/* Informations de base */}
      <div className='row mb-4'>
        <div className="col-md-6 mb-3">
          <label className="form-label fw-bold">Titre de l'article *</label>
          <input
            type="text"
            className="form-control"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            required
            placeholder="Entrez le titre de l'article..."
          />
        </div>
        <div className="col-md-6 mb-3">
          <label className="form-label fw-bold">Auteur *</label>
          <input
            type="text"
            className="form-control"
            value={author}
            onChange={(e) => setAuthor(e.target.value)}
            required
            placeholder="Nom de l'auteur..."
          />
        </div>
      </div>

      {/* Sections */}
      <div className="mb-4">
        <div className="d-flex justify-content-between align-items-center mb-3">
          <label className="form-label fw-bold mb-0">Sections du contenu</label>
          <button
            type="button"
            className="btn btn-outline-primary btn-sm"
            onClick={handleAddSection}
          >
            <i className='fa fa-plus'></i> Ajouter une section
          </button>
        </div>
        {sections.length === 0 && (
          <div className="alert alert-info">
            <i className="fa fa-info-circle me-2"></i>
            Aucune section ajoutée. Cliquez sur "Ajouter une section" pour commencer.
          </div>
        )}
        {sections.map((section, index) => (
          <SectionForm
            key={section.id}
            section={section}
            onChange={(updatedSection) => {
              const newSections = [...sections];
              newSections[index] = updatedSection;
              setSections(newSections);
            }}
            onRemove={() => handleRemoveSection(section.id)}
          />
        ))}
      </div>

      {/* Images */}
      <div className="mb-4">
        <div className="d-flex justify-content-between align-items-center mb-3">
          <label className="form-label fw-bold mb-0">Images de l'article</label>
          <small className="text-muted">Glissez-déposez ou cliquez pour ajouter des images</small>
        </div>

        <AddArticleImages
          addImage={handleAddImage}
          removeImage={handleRemoveImage}
          images={imageFiles}
          count={6}
        />

        {/* Légendes des images */}
        {imageFiles.filter(img => img.imageUrl).length > 0 && (
          <div className="mt-4">
            <h6 className="fw-bold mb-3">Légendes des images</h6>
            {imageFiles.map((image, index) => (
              <ArticleImageCaption
                key={index}
                image={image}
                index={index}
                onCaptionChange={handleImageCaptionChange}
                onRemove={handleRemoveImageByIndex}
              />
            ))}
          </div>
        )}
      </div>

      {/* Bouton de soumission */}
      <div className="d-flex justify-content-end gap-2 pt-3 border-top">
        <button
          type="submit"
          className="btn btn-success"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
              Enregistrement...
            </>
          ) : (
            <>
              <i className="fa fa-check me-2"></i>
              Enregistrer l'article
            </>
          )}
        </button>
      </div>
    </form>
  );
};

export default ArticleForm;
