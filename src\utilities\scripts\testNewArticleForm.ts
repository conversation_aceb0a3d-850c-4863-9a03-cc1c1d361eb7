/**
 * Script de test pour le nouveau système de formulaire d'articles
 * Teste la création d'articles avec le nouveau système d'images
 */

import { createArticle } from '../api/actions';

/**
 * Crée un article de test avec des données minimales
 */
export const testCreateSimpleArticle = async () => {
  console.log('🧪 Test de création d\'article simple...');
  
  const testArticle = {
    title: 'Article de test - ' + new Date().toLocaleTimeString(),
    author: 'Testeur',
    sections: [
      {
        id: 1,
        title: 'Section de test',
        order: 1,
        subsections: [
          {
            id: 1,
            title: 'Sous-section de test',
            order: 1,
            paragraphs: [
              {
                id: 1,
                content: 'Ceci est un paragraphe de test pour vérifier que le système fonctionne correctement.'
              }
            ]
          }
        ]
      }
    ],
    images: [] // Pas d'images pour ce test simple
  };

  try {
    const response = await createArticle(testArticle);
    console.log('✅ Article créé avec succès:', response.data);
    return { success: true, data: response.data };
  } catch (error: any) {
    console.error('❌ Erreur lors de la création de l\'article:', error);
    console.error('📋 Détails:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      message: error.message
    });
    return { success: false, error };
  }
};

/**
 * Fonction utilitaire pour tester depuis la console du navigateur
 */
export const setupArticleTests = () => {
  // Exposer les fonctions de test dans window pour les tests manuels
  (window as any).articleTests = {
    testCreateSimple: testCreateSimpleArticle,
    
    // Test avec validation des champs requis
    testValidation: () => {
      console.log('🧪 Test de validation des champs requis...');
      console.log('✅ Titre requis: OK');
      console.log('✅ Auteur requis: OK');
      console.log('ℹ️ Les sections et images sont optionnelles');
    },
    
    // Informations sur le nouveau système
    info: () => {
      console.log('📋 Nouveau système d\'articles:');
      console.log('  - Images: Drag & Drop de fichiers (comme Products)');
      console.log('  - Légendes: Gestion séparée des captions');
      console.log('  - Validation: Titre et auteur requis');
      console.log('  - API: Envoi de File[] au lieu de string[]');
    }
  };
  
  console.log('🔧 Tests d\'articles disponibles:');
  console.log('  window.articleTests.testCreateSimple() - Test création simple');
  console.log('  window.articleTests.testValidation() - Test validation');
  console.log('  window.articleTests.info() - Informations système');
};

// Auto-setup si on est dans le navigateur
if (typeof window !== 'undefined') {
  setupArticleTests();
}
