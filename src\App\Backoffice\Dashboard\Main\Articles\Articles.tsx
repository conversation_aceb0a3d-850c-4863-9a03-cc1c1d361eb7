import React from "react";
import Fade from "../../../../../utilities/minitiatures/Fade/Fade";
import ArticlesList from "./ArticlesList/ArticlesList";
import AddArticle from "./AddArticle/AddArticle";
import DeleteArticle from "./DeleteArticle/DeleteArticle";
import EditArticle from "./EditArticle/EditArticle";
import { refreshArticles } from "../../../../../utilities/redux/backoffice/backofficeSlice";
import { AppDispatch, Rootstate } from "../../../../../utilities/redux/store";
import { useDispatch, useSelector } from "react-redux";
import { Article } from "../../../../../utilities/constants/types";
import ArticlesEmpty from "./ArticlesEmpty/ArticlesEmpty";
import TablePlaceholder from "../../../../../utilities/minitiatures/TablePlaceholder/TablePlaceholder";
import ScrollEnd from "../../../../../utilities/minitiatures/ScrollEnd/ScrollEnd";
import "./Articles.scss";

// Import des scripts de migration pour le développement (Dashboard Admin)
import "../../../../../utilities/scripts/testArticleEndpoints";
import "../../../../../utilities/scripts/postBaseArticles";
import "../../../../../utilities/scripts/validateArticleStructure";
import "../../../../../utilities/scripts/checkAuthentication";
import "../../../../../utilities/scripts/fixArticleCategories";
import "../../../../../utilities/scripts/simpleArticleCreation";

// Message informatif pour le développement (Dashboard Admin)
console.log('🔧 OUTILS DE MIGRATION ARTICLES - DASHBOARD ADMIN');
console.log('📍 Vous êtes connecté en tant qu\'admin - Migration possible !');
console.log('');
console.log('🚀 Migration Simple (NOUVEAU - Recommandée):');
console.log('  window.simpleArticles.createAll() - Créer tous les articles');
console.log('  window.simpleArticles.testSingle() - Test simple');
console.log('');
console.log('🔍 Vérifications Disponibles:');
console.log('  window.articleCategoryFix.fetchCategories() - Voir les catégories');
console.log('  window.articleMigration.checkExistingArticles() - Vérifier articles existants');
console.log('  window.authCheck.checkAuthStatus() - Statut authentification');
console.log('');
console.log('⚠️ Migration Précédente (peut échouer):');
console.log('  window.articleMigration.postBaseArticles()');
console.log('');
console.log('📖 Voir ARTICLE_ENDPOINTS_SETUP.md pour plus de détails');

const DEFAULT_EDIT = {
    current: null as Article | null,
    setCurrent: (article: Article | null) => { article }
}

const DEFAULT_DELETE = {
    current: null as Article[] | null,
    setCurrent: (articles: Article[] | null) => { articles }
}

const ArticlesContext = React.createContext({
    edit: DEFAULT_EDIT,
    onDelete: DEFAULT_DELETE,
});

export const useEditArticle = () => {
    return React.useContext(ArticlesContext).edit;
}

export const useDeleteArticle = () => {
    return React.useContext(ArticlesContext).onDelete;
}

const Articles = React.memo(() => {

    const { articles } = useSelector((state: Rootstate) => state.backoffice);
    const dispatch = useDispatch<AppDispatch>();

    const [state, setState] = React.useState({
        edit: DEFAULT_EDIT,
        onDelete: DEFAULT_DELETE,
    });

    const edit = React.useMemo(() => {
        const setCurrent = (article: Article | null) => {
            setState(s => ({ ...s, edit: { ...s.edit, current: article } }));
        }

        return {
            current: state.edit.current,
            setCurrent
        }
    }, [state.edit.current]);

    const onDelete = React.useMemo(() => {
        const setCurrent = (articles: Article[] | null) => {
            setState(s => ({ ...s, onDelete: { ...s.onDelete, current: articles } }));
        }

        return {
            current: state.onDelete.current,
            setCurrent,
        }
    }, [state.onDelete.current]);

    React.useEffect(() => {
        if (!articles) {
            dispatch(refreshArticles());
        }
    }, [articles, dispatch]);

    return <ArticlesContext.Provider value={{ edit, onDelete }}>
        <div className="articles-container">
            <Fade show={Boolean(articles && articles.length > 0)}>
                <ArticlesList />
            </Fade>
            <Fade show={Boolean(articles && articles.length === 0)}>
                <ArticlesEmpty />
            </Fade>
            <Fade show={!articles}>
                <TablePlaceholder />
            </Fade>
            <AddArticle />
            <DeleteArticle />
            <EditArticle />
            <ScrollEnd
                show={true}
                whileInView={() => dispatch(refreshArticles())}
            >
                <div></div>
            </ScrollEnd>
        </div>
    </ArticlesContext.Provider>
});

export default Articles;
